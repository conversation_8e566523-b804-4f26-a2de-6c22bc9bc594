import { Column, CreateDate<PERSON><PERSON>umn, DeleteDate<PERSON><PERSON>umn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

enum status {
  ACTIVE = 'active',
  PAUSED = 'paused',
  TERMINATED = 'terminated',
  RESUMING = 'resuming',
}

enum addressType {
  WALLET = 'wallet',
  CONTRACT = 'contract',
}

@Entity('webhook')
export class Webhook {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int' })
  user_id: number;

  @Column({ type: 'int' })
  network_id: number;

  @Column({ type: 'varchar'})
  webhook_name: string;

  @Column({ type: 'varchar'})
  webhook_url: string;

  @Column({ type: 'varchar'})
  security_tokens: string // for HMAC signatures;

  @Column({ type: 'int' , default: 0 })
  start_position: number; 
  
  @Column({ type: 'enum', enum: status })
  status: status;

  @Column({ type: 'json', nullable: true })
  wallet_addresses: string[] | null; // wallet addresses being tracked

  @Column({ type: 'varchar', nullable: true , default: null})
  tags: string;

  @Column({ type: 'boolean', default: true})
  is_active: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true  , default: null })
  deleted_at: Date ;
}
