import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query, DefaultValuePipe, ParseIntPipe } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { ApiSecurity } from '@nestjs/swagger';
import { ApiKeyAuthGuard } from 'src/auth/api-key.guard';

@Controller('webhooks')
@UseGuards(ApiKeyAuthGuard)
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('create')
  @ApiSecurity('x-api-key')
  async create(@Body() createWebhookDto: CreateWebhookDto , @Request() req) {
    return this.webhooksService.createWebhook(createWebhookDto, req.user.id);
  }


  @Get('getAll')
  @ApiSecurity('x-api-key')
  async getAllWebhooks(
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('offset', new DefaultValuePipe(0), ParseIntPipe) offset: number,
    @Request() req ) {
    return this.webhooksService.getAllWebhooks(req.user.id, limit, offset);
  }

  @Get('get/:id')
  @ApiSecurity('x-api-key')
  async getWebhook(@Param('id') id: string) {
    return this.webhooksService.getWebhookById(id);
  }






}
